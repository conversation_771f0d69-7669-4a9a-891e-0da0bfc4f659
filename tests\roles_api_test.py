import requests
import logging
import os
import dotenv

from ..utils.utilities import CommonValidator

dotenv.load_dotenv()

logger = logging.getLogger(__name__)


class RolesAPIClient:
    def __init__(self):
        self.access_token = os.getenv("ACCESS_TOKEN")
        self.environment = os.getenv("ENVIRONMENT")
        self.base_url = f"https://console-{self.environment}-api.sanasinternal.com/api/v1"
        self.headers = {"Authorization": f"Bearer {self.access_token}"}
        self.group_id = "SANASB2C"
        self.role_data = {
            "name": "Test Role 1",
            "description": "Test Role 1 Description"
        }
        self.role_id = "test-role-1"


class TestRoleAPI:
    def setup_method(self):
        self.client = RolesAPIClient()

    # GET /groups/{groupId}/rbac/roles
    def test_get_roles(self):
        url = f"{self.client.base_url}/groups/{self.client.group_id}/rbac/roles"
        response = requests.get(url, headers=self.client.headers)
        logger.info(response.json())
        CommonValidator.validate_response_success(response)
        # validate this response - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'f024f389f3bdde5ed83d95d18d684eeb', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#read', 'role#create', 'role#update', 'permission#create', 'permission#update', 'platform_feature#read', 'permission#read', 'platform_feature#create', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account_settings#create', 'group_settings#create', 'role#update', 'account#read', 'billing_plan#read', 'group#update', 'endpoint#read', 'permission#update', 'account_settings#read', 'permission#create', 'feature#read', 'account_settings#update', 'role#create', 'portal_user#update', 'portal_user#read', 'endpoint#create', 'role#read', 'endpoint#update', 'group_settings#update', 'feature#create', 'feature#update', 'subscription#read', 'account#update', 'billing_plan#update', 'account#create', 'subscription#create', 'portal_user#create', 'group_settings#read', 'group#read', 'permission#read', 'group#create', 'subscription#update', 'billing_plan#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-************', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
        assert len(response.json()["data"]["roles"]) >= 2
        for role in response.json()["data"]["roles"]:
            assert role["groupId"] == self.client.group_id
            assert role["groupPath"] == "SANAS"
            assert role["isSystem"] is True
            if role["roleKey"] == "platform_admin":
                assert role["metadata"]["description"] == "Platform Admin Role"
                permissions = ['role#read', 'role#create', 'role#update', 'permission#create', 'permission#update',
                               'platform_feature#read', 'permission#read', 'platform_feature#create',
                               'platform_feature#update']
                assert set(role["permissions"]) == set(permissions)
                assert role["createdAt"] is not None
                assert role["createdBy"] is not None
                assert role["updatedAt"] is not None
                assert role["updatedBy"] is not None
                assert role["isSystem"] is True
                assert role["groupPath"] == "SANAS"
                assert role["groupPath"] == "SANAS"
                assert role["groupId"] == "SANASB2C"
                assert role["name"] == "Platform Admin"
                assert role["roleKey"] == "platform_admin"
                assert role["id"] is not None
                logger.info("-------------------------------")

            if role["roleKey"] == "super_admin":
                assert role["metadata"]["isSystemRole"] is True
                assert role["metadata"][
                           "description"] == "Default super admin role created during database initialization"
                assert role["createdAt"] is not None
                assert role["createdBy"] is not None
                assert role["updatedAt"] is not None
                assert role["updatedBy"] is not None
                assert role["groupPath"] == "SANAS"
                assert role["groupId"] == "SANASB2C"
                assert role["name"] == "super admin role"
                assert role["roleKey"] == "super_admin"
                assert role["id"] is not None

                permissions = [
                    'endpoint#read',
                    'group#read',
                    'account#update',
                    'group_settings#create',
                    'role#create',
                    'group_settings#read',
                    'permission#create',
                    'role#update',
                    'group#create',
                    'subscription#update',
                    'account_settings#update',
                    'account#create',
                    'account_settings#create',
                    'permission#update',
                    'subscription#read',
                    'portal_user#update',
                    'billing_plan#create',
                    'group#update',
                    'feature#read',
                    'billing_plan#read',
                    'portal_user#create',
                    'endpoint#create',
                    'feature#update',
                    'portal_user#read',
                    'group_settings#update',
                    'account#read',
                    'subscription#create',
                    'role#read',
                    'endpoint#update',
                    'account_settings#read',
                    'permission#read',
                    'feature#create',
                    'billing_plan#update'
                ]
                assert set(role["permissions"]) == set(permissions)

    def test_post_roles(self):
        url = f"{self.client.base_url}/groups/{self.client.group_id}/rbac/roles"
        response = requests.post(url, headers=self.client.headers, json=self.client.role_data)
        CommonValidator.validate_response_success(response)
        logger.info(response.json())
