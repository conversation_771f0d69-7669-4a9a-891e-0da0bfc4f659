import os
import dotenv
from utilities import code_challenge_generator, generate_random_string

dotenv.load_dotenv()


class ApiRouteHelper:
    def __init__(self):
        self.environment = os.environ.get("ENVIRONMENT", "dev")
        self.client_id = "portal-ui"
        self.audience = f"https://console-{self.environment}-api.sanasinternal.com"
        self.redirect_uri = f"https://console-{self.environment}.sanasinternal.com/callback"
        self.response_type = "code"
        self.scope = "openid+email+profile+offline_access"
        self.code_challenge_method = "S256"
        self.state = generate_random_string()
        self.nonce = generate_random_string()
        self.code_verifier, self.code_challenge = code_challenge_generator()

    def build_auth_url(self):
        return (f"https://identity-{self.environment}.sanasinternal.com/"
                f"?response_type={self.response_type}"
                f"&client_id={self.client_id}"
                f"&redirect_uri={self.redirect_uri}"
                f"&scope={self.scope}"
                f"&state={self.state}"
                f"&nonce={self.nonce}"
                f"&code_challenge={self.code_challenge}"
                f"&code_challenge_method={self.code_challenge_method}"
                f"&audience={self.audience}")

    def build_identifier_url(self):
        return f"https://console-{self.environment}.sanasinternal.com/identifier"

    def build_error_url(self):
        return f"https://console-{self.environment}.sanasinternal.com/error"
