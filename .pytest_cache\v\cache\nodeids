["client-login-test.py::TestClientLogin::test_successful_login", "client-token-generation-test.py::TestClientTokenGeneration::test_error_handling_for_failed_request", "client-token-generation-test.py::TestClientTokenGeneration::test_pkce_code_generation", "client-token-generation-test.py::TestClientTokenGeneration::test_successful_token_request", "client-token-generation-test.py::TestClientTokenGeneration::test_url_construction", "client-validation-test.py::TestClientTokenGeneration::test_csrf_protection_state_mismatch", "client-validation-test.py::TestClientTokenGeneration::test_duplicate_parameters", "client-validation-test.py::TestClientTokenGeneration::test_empty_audience", "client-validation-test.py::TestClientTokenGeneration::test_empty_nonce_parameter", "client-validation-test.py::TestClientTokenGeneration::test_empty_redirect_uri", "client-validation-test.py::TestClientTokenGeneration::test_empty_response_type", "client-validation-test.py::TestClientTokenGeneration::test_empty_scope", "client-validation-test.py::TestClientTokenGeneration::test_empty_state_parameter", "client-validation-test.py::TestClientTokenGeneration::test_error_handling_for_failed_request", "client-validation-test.py::TestClientTokenGeneration::test_extremely_long_parameter_values", "client-validation-test.py::TestClientTokenGeneration::test_invalid_audience", "client-validation-test.py::TestClientTokenGeneration::test_invalid_client_id", "client-validation-test.py::TestClientTokenGeneration::test_invalid_code_challenge", "client-validation-test.py::TestClientTokenGeneration::test_invalid_code_challenge_method", "client-validation-test.py::TestClientTokenGeneration::test_invalid_nonce_parameter", "client-validation-test.py::TestClientTokenGeneration::test_invalid_redirect_uri", "client-validation-test.py::TestClientTokenGeneration::test_invalid_response_type", "client-validation-test.py::TestClientTokenGeneration::test_invalid_scope", "client-validation-test.py::TestClientTokenGeneration::test_invalid_state_parameter", "client-validation-test.py::TestClientTokenGeneration::test_missing_audience", "client-validation-test.py::TestClientTokenGeneration::test_missing_code_challenge", "client-validation-test.py::TestClientTokenGeneration::test_missing_code_challenge_method", "client-validation-test.py::TestClientTokenGeneration::test_missing_nonce_parameter", "client-validation-test.py::TestClientTokenGeneration::test_missing_redirect_uri", "client-validation-test.py::TestClientTokenGeneration::test_missing_response_type", "client-validation-test.py::TestClientTokenGeneration::test_missing_scope", "client-validation-test.py::TestClientTokenGeneration::test_missing_state_parameter", "client-validation-test.py::TestClientTokenGeneration::test_multiple_parameters_missing", "client-validation-test.py::TestClientTokenGeneration::test_null_bytes_in_parameters", "client-validation-test.py::TestClientTokenGeneration::test_pkce_code_challenge_validation", "client-validation-test.py::TestClientTokenGeneration::test_pkce_code_generation", "client-validation-test.py::TestClientTokenGeneration::test_redirect_uri_open_redirect_protection", "client-validation-test.py::TestClientTokenGeneration::test_redirect_uri_subdomain_attack", "client-validation-test.py::TestClientTokenGeneration::test_special_characters_in_parameters", "client-validation-test.py::TestClientTokenGeneration::test_successful_token_request", "client-validation-test.py::TestClientTokenGeneration::test_unicode_characters_in_parameters", "client-validation-test.py::TestClientTokenGeneration::test_url_construction", "client-validation-test.py::TestClientTokenGeneration::test_url_encoded_special_characters", "group-api-test.py::TestGroupAPI::test_create_group", "group-api-test.py::TestGroupAPI::test_get_group_by_group_id", "group-api-test.py::TestGroupAPI::test_get_system_groups", "group-api-test.py::TestGroupAPI::test_group_tree", "group-api-test.py::test_create_group", "group-api-test.py::test_get_group_by_group_id", "group-api-test.py::test_group_tree", "roles_api_test.py::TestRoleAPI::test_get_roles", "roles_api_test.py::TestRoleAPI::test_post_roles", "tests/roles_api_test.py::TestRoleAPI::test_get_roles", "tests/roles_api_test.py::TestRoleAPI::test_post_roles"]