import requests
import logging
import os
import dotenv
from typing import Dict, Any, List

dotenv.load_dotenv()

logger = logging.getLogger(__name__)

class GroupAPIClient:
    def __init__(self):
        self.access_token = os.getenv("ACCESS_TOKEN")
        self.environment = os.getenv("ENVIRONMENT")
        self.base_url = f"https://console-{self.environment}-api.sanasinternal.com/api/v1"
        self.headers = {"Authorization": f"Bearer {self.access_token}"}
    
    def get_groups(self, include_system: bool = False) -> requests.Response:
        url = f"{self.base_url}/groups"
        params = {"includeSystem": "true"} if include_system else {}
        return requests.get(url, headers=self.headers, params=params)
    
    def get_group_tree(self, include_system: bool = False) -> requests.Response:
        url = f"{self.base_url}/groups/tree"
        params = {"includeSystem": "true"} if include_system else {}
        return requests.get(url, headers=self.headers, params=params)
    
    def create_group(self, parent_id: str, group_data: Dict[str, Any]) -> requests.Response:
        url = f"{self.base_url}/groups/{parent_id}"
        return requests.post(url, headers=self.headers, json=group_data)
    
    def get_group_by_id(self, group_id: str, include_settings: bool = False) -> requests.Response:
        url = f"{self.base_url}/groups/{group_id}"
        params = {"includeSettings": "true"} if include_settings else {}
        return requests.get(url, headers=self.headers, params=params)


class GroupValidator:
    @staticmethod
    def validate_response_success(response: requests.Response) -> None:
        assert response.status_code == 200
        assert response.json()["success"] is True
    
    @staticmethod
    def validate_system_groups(groups: List[Dict[str, Any]]) -> None:
        expected_group_ids = {"SANASB2B", "SANASB2C", "SANAS"}
        system_groups = [g for g in groups if g["groupType"] == "SYSTEM"]
        
        assert len(system_groups) == 3
        
        actual_group_ids = {g["groupId"] for g in system_groups}
        assert actual_group_ids == expected_group_ids
        
        for group in system_groups:
            assert group["meta"] == {}
            assert group["status"] == "ACTIVE"
    
    @staticmethod
    def validate_response_meta(meta: Dict[str, Any]) -> None:
        required_fields = {"page", "perPage", "sortBy", "sortOrder", "query", "total"}
        assert all(field in meta for field in required_fields)
    
    @staticmethod
    def validate_group_details(group_data: Dict[str, Any], expected_group_id: str) -> None:
        assert group_data["groupId"] == expected_group_id
        assert group_data["parentGroupId"] is None
        assert group_data["path"] == f"SANAS.{expected_group_id}"
        assert group_data["groupType"] == "SYSTEM"
        assert group_data["status"] == "ACTIVE"
        assert group_data["meta"] == {}
        assert group_data["createdAt"] is not None
        assert group_data["updatedAt"] is not None


class TestGroupAPI:
    def setup_method(self):
        self.client = GroupAPIClient()
        self.validator = GroupValidator()

    def test_get_system_groups(self):
        response = self.client.get_groups(include_system=True)
        self.validator.validate_response_success(response)
        
        response_json = response.json()
        system_groups = response_json["data"]
        
        self.validator.validate_system_groups(system_groups)
        self.validator.validate_response_meta(response_json["meta"])

    def test_group_tree(self):
        response = self.client.get_group_tree(include_system=True)
        self.validator.validate_response_success(response)
        logger.info(response.json())


    def test_create_group(self):
        parent_id = "SANASB2C"
        group_data = {
            "groupType": "ACCOUNT",
            "name": "Test Group 1",
            "parentGroupId": parent_id
        }
        
        response = self.client.create_group(parent_id, group_data)
        logger.info(response.text)
        self.validator.validate_response_success(response)
    
    def test_get_group_by_group_id(self):
        group_id = "SANASB2C"
        response = self.client.get_group_by_id(group_id, include_settings=True)
        self.validator.validate_response_success(response)
        
        logger.info(response.json())
        group_data = response.json()["data"]
        self.validator.validate_group_details(group_data, group_id)
