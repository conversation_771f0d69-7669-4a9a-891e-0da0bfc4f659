2025-08-28 20:59:37,018 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:59:38,320 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 20:59:38,322 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'cec230b2de0716098523e5bf40c82e4e', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#update', 'platform_feature#create', 'platform_feature#update', 'role#update', 'permission#create', 'role#read', 'role#create', 'platform_feature#read', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['group_settings#update', 'permission#read', 'feature#read', 'account_settings#update', 'account#create', 'portal_user#create', 'endpoint#read', 'account#update', 'group_settings#create', 'billing_plan#update', 'permission#update', 'subscription#read', 'group#update', 'billing_plan#read', 'group_settings#read', 'portal_user#update', 'role#read', 'account_settings#read', 'role#update', 'subscription#update', 'account#read', 'subscription#create', 'endpoint#create', 'group#read', 'permission#create', 'group#create', 'endpoint#update', 'feature#update', 'role#create', 'portal_user#read', 'billing_plan#create', 'feature#create', 'account_settings#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-************', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 20:59:38,324 [INFO] roles_api_test - -------------------------------
2025-08-28 20:59:38,332 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:59:39,265 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 400 111
2025-08-28 21:00:01,004 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 21:00:02,095 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 21:00:02,095 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'd7fe693c1ae396a4f7b0a824561e576a', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#read', 'role#create', 'platform_feature#read', 'permission#read', 'platform_feature#update', 'permission#create', 'permission#update', 'platform_feature#create', 'role#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['feature#update', 'account_settings#read', 'account#update', 'subscription#create', 'permission#create', 'account_settings#update', 'group_settings#read', 'feature#read', 'role#create', 'account#create', 'permission#update', 'role#read', 'group_settings#update', 'feature#create', 'account#read', 'billing_plan#update', 'endpoint#update', 'account_settings#create', 'group_settings#create', 'permission#read', 'billing_plan#create', 'group#update', 'group#create', 'billing_plan#read', 'subscription#read', 'subscription#update', 'portal_user#update', 'group#read', 'role#update', 'endpoint#create', 'endpoint#read', 'portal_user#create', 'portal_user#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-************', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 21:00:02,095 [INFO] roles_api_test - -------------------------------
2025-08-28 21:00:02,108 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 21:00:02,903 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 400 111
