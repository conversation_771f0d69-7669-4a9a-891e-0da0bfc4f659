<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="fake_useragent" />
            <item index="1" class="java.lang.String" itemvalue="python-saml" />
            <item index="2" class="java.lang.String" itemvalue="faiss_cpu" />
            <item index="3" class="java.lang.String" itemvalue="aiohttp" />
            <item index="4" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="5" class="java.lang.String" itemvalue="volcengine-python-sdk" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>