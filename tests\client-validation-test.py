import requests
from urllib.parse import urlparse, parse_qs

import logging

from utils.apiroutehelper import <PERSON><PERSON><PERSON>outeHelper

logger = logging.getLogger(__name__)

class TestClientTokenGeneration:
    def setup_method(self):
        self.helper = ApiRouteHelper()
        self.environment = self.helper.environment
        self.client_id = self.helper.client_id
        self.audience = self.helper.audience
        self.redirect_uri = self.helper.redirect_uri
        self.response_type = self.helper.response_type
        self.scope = self.helper.scope
        self.code_challenge_method = self.helper.code_challenge_method

    def test_successful_token_request(self):
        url = self.helper.build_auth_url()
        token_response = requests.get(url)

        assert token_response.status_code == 200

        if token_response.url.startswith(self.helper.build_identifier_url()):
            assert f"{self.helper.build_identifier_url()}?login_challenge" in token_response.url
            assert "login_challenge" in token_response.url
    
    def test_url_construction(self):
        url = self.helper.build_auth_url()
        assert f"response_type={self.response_type}" in url
        assert f"client_id={self.client_id}" in url
        assert f"redirect_uri={self.redirect_uri}" in url
        assert f"scope={self.scope}" in url
        assert f"state={self.helper.state}" in url
        assert f"nonce={self.helper.nonce}" in url
        assert f"code_challenge={self.helper.code_challenge}" in url
        assert f"code_challenge_method={self.code_challenge_method}" in url
        assert f"audience={self.audience}" in url


    def test_invalid_client_id(self):
        invalid_client_id = "invalid_client_id"
        url = self.helper.build_auth_url()
        url = url.replace(self.client_id, invalid_client_id)
        token_response = requests.get(url)
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            
            assert "error" in query_params
            assert "error_description" in query_params
            
            error_type = query_params["error"][0]
            error_description = query_params["error_description"][0]
            
            assert error_type is not None
            assert error_description is not None

    def test_invalid_redirect_uri(self):
        invalid_redirect_uri = "https://malicious-site.com/callback"
        url = self.helper.build_auth_url()
        url = url.replace(self.redirect_uri, invalid_redirect_uri)
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] in ["invalid_request", "invalid_client"]

    def test_missing_redirect_uri(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&redirect_uri={self.redirect_uri}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_request"

    def test_empty_redirect_uri(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"redirect_uri={self.redirect_uri}", "redirect_uri=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_response_type(self):
        invalid_response_type = "invalid_type"
        url = self.helper.build_auth_url()
        url = url.replace(f"response_type={self.response_type}", f"response_type={invalid_response_type}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "unsupported_response_type"

    def test_missing_response_type(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"response_type={self.response_type}&", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_request"

    def test_empty_response_type(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"response_type={self.response_type}", "response_type=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_scope(self):
        invalid_scope = "invalid_scope"
        url = self.helper.build_auth_url()
        url = url.replace(f"scope={self.scope}", f"scope={invalid_scope}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_scope"

    def test_missing_scope(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&scope={self.scope}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_request"

    def test_empty_scope(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"scope={self.scope}", "scope=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_audience(self):
        invalid_audience = "https://malicious-api.com"
        url = self.helper.build_auth_url()
        url = url.replace(self.audience, invalid_audience)
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] in ["invalid_request", "invalid_client"]

    def test_missing_audience(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&audience={self.audience}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_empty_audience(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"audience={self.audience}", "audience=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_code_challenge(self):
        invalid_code_challenge = "invalid_challenge_123"
        url = self.helper.build_auth_url()
        url = url.replace(f"code_challenge={self.helper.code_challenge}", f"code_challenge={invalid_code_challenge}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_request"

    def test_missing_code_challenge(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&code_challenge={self.helper.code_challenge}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_code_challenge_method(self):
        invalid_method = "invalid_method"
        url = self.helper.build_auth_url()
        url = url.replace(f"code_challenge_method={self.code_challenge_method}", f"code_challenge_method={invalid_method}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params
            assert query_params["error"][0] == "invalid_request"

    def test_missing_code_challenge_method(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&code_challenge_method={self.code_challenge_method}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_invalid_state_parameter(self):
        invalid_state = "malicious_state_value"
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", f"state={invalid_state}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_missing_state_parameter(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&state={self.helper.state}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_empty_state_parameter(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", "state=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_invalid_nonce_parameter(self):
        invalid_nonce = "malicious_nonce_value"
        url = self.helper.build_auth_url()
        url = url.replace(f"nonce={self.helper.nonce}", f"nonce={invalid_nonce}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_missing_nonce_parameter(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&nonce={self.helper.nonce}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_empty_nonce_parameter(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"nonce={self.helper.nonce}", "nonce=")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_special_characters_in_parameters(self):
        special_chars = "<script>alert('xss')</script>"
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", f"state={special_chars}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_url_encoded_special_characters(self):
        encoded_chars = "%3Cscript%3Ealert%28%27xss%27%29%3C%2Fscript%3E"
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", f"state={encoded_chars}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_null_bytes_in_parameters(self):
        null_byte_value = "valid%00malicious"
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", f"state={null_byte_value}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_unicode_characters_in_parameters(self):
        unicode_value = "测试值"
        url = self.helper.build_auth_url()
        url = url.replace(f"state={self.helper.state}", f"state={unicode_value}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_csrf_protection_state_mismatch(self):
        original_state = self.helper.state
        different_state = self.helper.generate_random_string(15)
        
        url = self.helper.build_auth_url()
        url = url.replace(f"state={original_state}", f"state={different_state}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_pkce_code_challenge_validation(self):
        import base64
        import hashlib
        
        fake_verifier = "fake_code_verifier_that_wont_match_challenge"
        fake_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(fake_verifier.encode()).digest()
        ).decode().rstrip('=')
        
        url = self.helper.build_auth_url()
        url = url.replace(f"code_challenge={self.helper.code_challenge}", f"code_challenge={fake_challenge}")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200

    def test_redirect_uri_open_redirect_protection(self):
        malicious_redirect = "https://attacker.com/steal-tokens"
        url = self.helper.build_auth_url()
        url = url.replace(self.redirect_uri, malicious_redirect)
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_redirect_uri_subdomain_attack(self):
        subdomain_attack = "https://evil.sanasinternal.com/callback"
        url = self.helper.build_auth_url()
        url = url.replace(self.redirect_uri, subdomain_attack)
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_multiple_parameters_missing(self):
        url = self.helper.build_auth_url()
        url = url.replace(f"&client_id={self.client_id}", "")
        url = url.replace(f"&redirect_uri={self.redirect_uri}", "")
        url = url.replace(f"&scope={self.scope}", "")
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
        if token_response.url.startswith(self.helper.build_error_url()):
            parsed_url = urlparse(token_response.url)
            query_params = parse_qs(parsed_url.query)
            assert "error" in query_params

    def test_duplicate_parameters(self):
        url = self.helper.build_auth_url()
        url += f"&client_id=duplicate_client_id"
        token_response = requests.get(url)
        
        assert token_response.status_code == 200
