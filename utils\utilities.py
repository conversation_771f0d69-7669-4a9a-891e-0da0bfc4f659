import hashlib
import secrets
import string
import random

import pkce
import requests


def sha256_digest(text: str) -> str:
    return hashlib.sha256(text.encode("utf-8")).hexdigest()


def random_string(length=16):
    chars = string.ascii_letters + string.digits
    return ''.join(secrets.choice(chars) for _ in range(length))


def code_challenge_generator():
    code_verifier, code_challenge = pkce.generate_pkce_pair(code_verifier_length=64)
    return code_verifier, code_challenge


def generate_random_string(length=10):
    return ''.join(random.choice(string.ascii_letters) for _ in range(length))


class CommonValidator:

    @staticmethod
    def validate_response_success(response: requests.Response) -> None:
        assert response.status_code == 200
        assert response.json()["success"] is True
