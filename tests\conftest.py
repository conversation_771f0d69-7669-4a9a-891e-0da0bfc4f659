import logging
from logging.handlers import RotatingFileHandler
import pytest

@pytest.fixture(autouse=True, scope="session")
def configure_logging():
    log_file = "test_stdout.log"

    # Create rotating file handler
    rotating_handler = RotatingFileHandler(
        log_file,
        maxBytes=5 * 1024 * 1024,  # 5 MB per file
        backupCount=10,             # keep last 3 backups
    )

    formatter = logging.Formatter(
        "%(asctime)s [%(levelname)s] %(name)s - %(message)s"
    )
    rotating_handler.setFormatter(formatter)

    # Stream handler for console output
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # or INFO
    root_logger.handlers = [rotating_handler, console_handler]

    root_logger.info("Rolling file logging initialized → writing to %s", log_file)
