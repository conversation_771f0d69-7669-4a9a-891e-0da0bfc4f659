2025-08-28 17:22:03,216 [INFO] client-login-test - --------------------
2025-08-28 17:22:03,216 [INFO] client-login-test - Starting Test for TestClientLogin
2025-08-28 17:22:03,216 [INFO] client-login-test - --------------------
2025-08-28 17:22:03,216 [INFO] client-login-test - code_verifier: 2M9nCUeDrwrwyIkilcJDJQgkmzcTAQf6e28R0QPBoe9SrbGYUP3beJEUU1KmqBsGTId1tvZL91tuWuFvjV2PZqtODeZ9t8BhR0yvTjb13FvVBCGUkrLMgqYscsktF9zW
2025-08-28 17:22:03,217 [INFO] client-login-test - Auth URL: https://identity-dev.sanasinternal.com/?response_type=code&client_id=portal-ui&redirect_uri=https://console-dev.sanasinternal.com/callback&scope=openid+email+profile+offline_access&state=FQreiwEcBo&nonce=ioAabBqKWe&code_challenge=e8a00483932b8afa7f599fc1077600be807167422b96fdf6e3d9b2dcb274993b&code_challenge_method=S256&audience=https://console-dev-api.sanasinternal.com
2025-08-28 17:25:07,809 [INFO] client-login-test - --------------------
2025-08-28 17:25:07,809 [INFO] client-login-test - Starting Test for TestClientLogin
2025-08-28 17:25:07,809 [INFO] client-login-test - --------------------
2025-08-28 17:25:07,823 [INFO] client-login-test - code_verifier: WxHJf7-L8Q17DrMNWZ2mVsvEbth_GZbVf6DM3uFj_zPRekXZPg1fr-GAcAjQY6Qs
2025-08-28 17:25:07,824 [INFO] client-login-test - Auth URL: https://identity-dev.sanasinternal.com/?response_type=code&client_id=portal-ui&redirect_uri=https://console-dev.sanasinternal.com/callback&scope=openid+email+profile+offline_access&state=OyTjJTVxxr&nonce=TTHKiZCfhW&code_challenge=MtyuqrNgnS8ZTpq_JWlJCL3Ad09WQPWqxqre7W8HZJM&code_challenge_method=S256&audience=https://console-dev-api.sanasinternal.com
2025-08-28 18:19:24,884 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:19:25,636 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:21:01,683 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:21:02,374 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:24:44,160 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:24:44,920 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:24:44,936 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:24:45,294 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/test-group-1 HTTP/1.1" 404 None
2025-08-28 18:26:03,381 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:26:03,946 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:26:03,969 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:26:04,450 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/aaaa HTTP/1.1" 404 None
2025-08-28 18:27:17,131 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:27:17,877 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:27:17,885 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:27:18,354 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/aaaa HTTP/1.1" 404 None
2025-08-28 18:30:03,180 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:30:03,901 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:30:03,910 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:30:04,439 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:30:04,457 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:30:04,940 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/aaaa HTTP/1.1" 404 None
2025-08-28 18:32:05,152 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:32:05,797 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:32:05,813 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:32:06,431 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:32:06,442 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:32:06,871 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 422 128
2025-08-28 18:33:12,974 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:33:13,407 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:33:13,423 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:33:13,971 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:33:13,979 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:33:14,481 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 422 128
2025-08-28 18:35:06,595 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:07,302 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:35:07,312 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:07,835 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:35:07,842 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:08,296 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANAS.SANASB2C HTTP/1.1" 404 None
2025-08-28 18:35:32,235 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:32,634 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:35:32,638 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:33,018 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:35:33,022 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:35:33,414 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 422 128
2025-08-28 18:40:35,460 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:40:36,239 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:40:36,245 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:40:36,782 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:40:36,787 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:40:37,320 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 422 128
2025-08-28 18:41:05,581 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:06,081 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:41:06,089 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:06,641 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:41:06,649 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:07,044 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 422 128
2025-08-28 18:41:07,045 [INFO] group-api-test - {"statusCode":422,"errorCode":"INVALID_PARENT_GROUP","message":"Parent group does not exist or is inaccessible.","details":null}
2025-08-28 18:41:48,817 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:49,316 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:41:49,322 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:49,858 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:41:49,866 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:41:50,374 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2B HTTP/1.1" 422 128
2025-08-28 18:41:50,374 [INFO] group-api-test - {"statusCode":422,"errorCode":"INVALID_PARENT_GROUP","message":"Parent group does not exist or is inaccessible.","details":null}
2025-08-28 18:44:30,678 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:44:31,563 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:44:31,572 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:44:32,146 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:44:32,147 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': '9c6a77374c0c259e898018f11dd7893c', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 18:44:32,154 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:44:32,618 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 18:44:32,618 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 18:45:49,040 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:45:49,777 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:45:49,786 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:45:50,300 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:45:50,301 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': 'fc352225cb43a81fdb8f23268213bff4', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 18:45:50,308 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:45:50,918 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 18:45:50,918 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 18:50:13,424 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:50:15,137 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:50:15,147 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:50:15,974 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:50:15,976 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': 'b72602d265887ec19800780e0ff41a57', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 18:50:15,985 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:50:16,754 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 18:50:16,754 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 18:53:44,201 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:53:44,998 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:53:45,006 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:53:45,495 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 18:53:45,496 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': '4e9af733271f8e71a84166f308c23107', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 18:53:45,501 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 18:53:46,040 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 18:53:46,041 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:37:56,615 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:37:57,702 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:37:57,702 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:37:59,129 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:37:59,129 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': 'd274a18ad900cb82fb5e6feb3bc93037', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 19:37:59,144 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:37:59,926 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 19:37:59,926 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:38:00,101 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:38:01,180 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C?includeSettings=true HTTP/1.1" 200 None
2025-08-28 19:38:16,629 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:38:17,638 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:38:17,650 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:38:18,680 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:38:18,695 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': 'ff8d2f107c10a91c5148eec9295e1aaf', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 19:38:18,703 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:38:19,635 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 19:38:19,635 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:38:19,772 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:38:20,472 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C?includeSettings=true HTTP/1.1" 200 None
2025-08-28 19:38:20,474 [INFO] group-api-test - {'success': True, 'message': 'Group retrieved successfully', 'statusCode': 200, 'requestId': '084f5d49b8443959380bc249a9b30bf8', 'data': {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}}
2025-08-28 19:39:23,456 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:39:24,380 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:39:24,390 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:39:25,098 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:39:25,100 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': 'bfe5dc0d58d2f94975f1db8d0dd8233f', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 19:39:25,100 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:39:25,969 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 19:39:25,972 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:39:26,108 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:39:27,141 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C?includeSettings=true HTTP/1.1" 200 None
2025-08-28 19:39:27,141 [INFO] group-api-test - {'success': True, 'message': 'Group retrieved successfully', 'statusCode': 200, 'requestId': 'e7fdd86eb5591408e1ba8ef554ef445f', 'data': {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}}
2025-08-28 19:43:30,309 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:43:31,591 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:43:31,604 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:43:32,488 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:43:32,492 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': '0d27be44c1f88bd91c591fea6709423e', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 19:43:32,528 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:43:33,484 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 19:43:33,486 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:43:33,610 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:43:34,664 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C?includeSettings=true HTTP/1.1" 200 None
2025-08-28 19:43:34,665 [INFO] group-api-test - {'success': True, 'message': 'Group retrieved successfully', 'statusCode': 200, 'requestId': '3a56e7c6f86efb5c22cb0ae8d053255b', 'data': {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}}
2025-08-28 19:51:41,372 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:51:42,568 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:51:42,582 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:51:43,540 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/tree?includeSystem=true HTTP/1.1" 200 None
2025-08-28 19:51:43,540 [INFO] group-api-test - {'success': True, 'message': 'Groups retrieved successfully', 'statusCode': 200, 'requestId': '22030f899aea0b610206df51600f4d80', 'data': [{'groupId': 'SANAS', 'name': 'Sanas', 'parentGroupId': None, 'path': 'SANAS', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2B', 'name': 'B2B', 'parentGroupId': None, 'path': 'SANAS.SANASB2B', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}, {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}], 'meta': {'page': 1, 'perPage': 20, 'sortBy': 'path', 'sortOrder': 'asc', 'query': '', 'total': 3}}
2025-08-28 19:51:43,572 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:51:44,527 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C HTTP/1.1" 500 117
2025-08-28 19:51:44,542 [INFO] group-api-test - {"statusCode":500,"errorCode":"INTERNAL_SERVER_ERROR","message":"Failed to check for group existence","details":null}
2025-08-28 19:51:44,685 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:51:45,563 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C?includeSettings=true HTTP/1.1" 200 None
2025-08-28 19:51:45,579 [INFO] group-api-test - {'success': True, 'message': 'Group retrieved successfully', 'statusCode': 200, 'requestId': '3f7c8a9126d98e92050d88249ef79726', 'data': {'groupId': 'SANASB2C', 'name': 'B2C', 'parentGroupId': None, 'path': 'SANAS.SANASB2C', 'licenseKey': None, 'status': 'ACTIVE', 'groupType': 'SYSTEM', 'meta': {}, 'createdAt': '2025-08-28T05:34:30.346897Z', 'updatedAt': '2025-08-28T05:34:30.346897Z'}}
2025-08-28 19:51:59,657 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:52:00,648 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:52:00,648 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'f024f389f3bdde5ed83d95d18d684eeb', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#read', 'role#create', 'role#update', 'permission#create', 'permission#update', 'platform_feature#read', 'permission#read', 'platform_feature#create', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account_settings#create', 'group_settings#create', 'role#update', 'account#read', 'billing_plan#read', 'group#update', 'endpoint#read', 'permission#update', 'account_settings#read', 'permission#create', 'feature#read', 'account_settings#update', 'role#create', 'portal_user#update', 'portal_user#read', 'endpoint#create', 'role#read', 'endpoint#update', 'group_settings#update', 'feature#create', 'feature#update', 'subscription#read', 'account#update', 'billing_plan#update', 'account#create', 'subscription#create', 'portal_user#create', 'group_settings#read', 'group#read', 'permission#read', 'group#create', 'subscription#update', 'billing_plan#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:53:30,170 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:53:31,271 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:53:31,271 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '555402e38ed83086db2187c7ac8dc2c1', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#create', 'role#read', 'platform_feature#read', 'permission#read', 'platform_feature#update', 'role#create', 'permission#update', 'platform_feature#create', 'role#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account#read', 'endpoint#create', 'billing_plan#create', 'portal_user#create', 'role#read', 'group#read', 'group_settings#read', 'account#create', 'endpoint#read', 'subscription#read', 'account_settings#create', 'subscription#create', 'billing_plan#update', 'portal_user#read', 'account#update', 'group_settings#create', 'permission#create', 'permission#read', 'feature#read', 'group#create', 'permission#update', 'feature#update', 'account_settings#read', 'role#update', 'subscription#update', 'feature#create', 'account_settings#update', 'role#create', 'billing_plan#read', 'group_settings#update', 'portal_user#update', 'endpoint#update', 'group#update'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:54:20,880 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:54:21,950 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:54:21,964 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '0d80f9795d941f7948bce6672036b3b6', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#update', 'permission#create', 'role#read', 'role#create', 'platform_feature#read', 'platform_feature#create', 'platform_feature#update', 'permission#update', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account_settings#update', 'account#read', 'billing_plan#read', 'billing_plan#update', 'endpoint#create', 'endpoint#read', 'account_settings#read', 'group_settings#create', 'portal_user#create', 'subscription#create', 'role#read', 'account_settings#create', 'group#update', 'group#create', 'endpoint#update', 'group#read', 'subscription#update', 'feature#create', 'group_settings#read', 'portal_user#update', 'portal_user#read', 'subscription#read', 'group_settings#update', 'permission#read', 'role#update', 'feature#update', 'role#create', 'permission#update', 'permission#create', 'billing_plan#create', 'account#create', 'account#update', 'feature#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:55:32,007 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:55:32,953 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:55:32,955 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '169abb09432675461d937a28829e4e31', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#update', 'platform_feature#create', 'platform_feature#update', 'permission#create', 'role#create', 'platform_feature#read', 'permission#read', 'role#update', 'role#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['permission#create', 'role#create', 'account#create', 'account_settings#read', 'subscription#read', 'group_settings#create', 'account_settings#update', 'portal_user#update', 'role#update', 'group_settings#read', 'portal_user#read', 'endpoint#read', 'role#read', 'group#read', 'account#read', 'billing_plan#update', 'endpoint#create', 'group#create', 'account#update', 'permission#read', 'billing_plan#create', 'subscription#create', 'group_settings#update', 'group#update', 'subscription#update', 'feature#create', 'permission#update', 'endpoint#update', 'feature#read', 'billing_plan#read', 'portal_user#create', 'feature#update', 'account_settings#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:56:18,761 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:56:19,706 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:56:19,706 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'dbecfd6c2755f08d64bb6a78f7fa7bff', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#create', 'role#read', 'platform_feature#create', 'platform_feature#update', 'role#update', 'role#create', 'permission#update', 'platform_feature#read', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['feature#create', 'billing_plan#update', 'group_settings#read', 'account#create', 'endpoint#read', 'permission#update', 'group_settings#create', 'feature#read', 'portal_user#update', 'endpoint#create', 'role#update', 'account_settings#update', 'account_settings#read', 'account_settings#create', 'account#update', 'billing_plan#read', 'portal_user#create', 'role#read', 'subscription#read', 'group#read', 'group_settings#update', 'subscription#update', 'account#read', 'subscription#create', 'feature#update', 'permission#create', 'permission#read', 'group#update', 'endpoint#update', 'group#create', 'role#create', 'portal_user#read', 'billing_plan#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:57:23,161 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:57:24,378 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:57:24,383 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '3ff3affe61ab08c74273edf8fd2b39c0', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['platform_feature#update', 'role#update', 'permission#create', 'role#read', 'role#create', 'permission#update', 'permission#read', 'platform_feature#create', 'platform_feature#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['group#read', 'account#update', 'role#update', 'account#read', 'permission#create', 'billing_plan#create', 'endpoint#update', 'group_settings#update', 'permission#read', 'group#update', 'account_settings#update', 'role#create', 'portal_user#create', 'billing_plan#update', 'group_settings#read', 'account#create', 'role#read', 'group_settings#create', 'feature#read', 'feature#update', 'group#create', 'billing_plan#read', 'portal_user#update', 'subscription#create', 'endpoint#create', 'permission#update', 'account_settings#read', 'subscription#read', 'subscription#update', 'feature#create', 'portal_user#read', 'account_settings#create', 'endpoint#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:58:08,289 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:58:10,337 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:58:10,341 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'c7ef5a7ef81ee213b642205a9d6aa3db', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#update', 'permission#create', 'permission#read', 'platform_feature#create', 'role#read', 'role#create', 'permission#update', 'platform_feature#read', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account_settings#read', 'group_settings#update', 'group#create', 'billing_plan#create', 'portal_user#read', 'group#read', 'permission#create', 'group_settings#read', 'account#create', 'endpoint#create', 'endpoint#read', 'permission#read', 'subscription#update', 'account_settings#update', 'role#create', 'role#read', 'account#update', 'billing_plan#update', 'subscription#create', 'permission#update', 'endpoint#update', 'feature#create', 'account#read', 'portal_user#create', 'feature#update', 'account_settings#create', 'portal_user#update', 'subscription#read', 'group_settings#create', 'group#update', 'role#update', 'feature#read', 'billing_plan#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:58:10,341 [INFO] roles_api_test - {'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#update', 'permission#create', 'permission#read', 'platform_feature#create', 'role#read', 'role#create', 'permission#update', 'platform_feature#read', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}
2025-08-28 19:58:42,135 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:58:43,084 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:58:43,092 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '1b185e8a45cc521a69224aebd985bc04', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#create', 'role#read', 'role#create', 'permission#update', 'permission#read', 'role#update', 'platform_feature#read', 'platform_feature#create', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account#create', 'endpoint#create', 'billing_plan#create', 'feature#create', 'portal_user#create', 'feature#update', 'account_settings#create', 'billing_plan#read', 'portal_user#update', 'permission#update', 'group_settings#create', 'account#read', 'portal_user#read', 'account_settings#read', 'group#read', 'account#update', 'role#update', 'billing_plan#update', 'subscription#create', 'group#update', 'subscription#read', 'group#create', 'role#create', 'group_settings#read', 'role#read', 'group_settings#update', 'permission#read', 'subscription#update', 'permission#create', 'account_settings#update', 'endpoint#read', 'endpoint#update', 'feature#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:58:43,092 [INFO] roles_api_test - {'description': 'Platform Admin Role'}
2025-08-28 19:59:25,788 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 19:59:27,015 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 19:59:27,015 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'f3c85c431a39383d1a1726e0e3aa19f1', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#read', 'role#update', 'permission#create', 'permission#update', 'platform_feature#read', 'platform_feature#create', 'platform_feature#update', 'role#read', 'role#create'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['endpoint#read', 'group#read', 'account#update', 'group_settings#create', 'role#create', 'group_settings#read', 'permission#create', 'role#update', 'group#create', 'subscription#update', 'account_settings#update', 'account#create', 'account_settings#create', 'permission#update', 'subscription#read', 'portal_user#update', 'billing_plan#create', 'group#update', 'feature#read', 'billing_plan#read', 'portal_user#create', 'endpoint#create', 'feature#update', 'portal_user#read', 'group_settings#update', 'account#read', 'subscription#create', 'role#read', 'endpoint#update', 'account_settings#read', 'permission#read', 'feature#create', 'billing_plan#update'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 19:59:27,015 [INFO] roles_api_test - -------------------------
2025-08-28 19:59:27,015 [INFO] roles_api_test - {'description': 'Platform Admin Role'}
2025-08-28 20:00:26,090 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:00:27,063 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 20:00:27,066 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'c787738288ec8ff68f7ff7f79ccaf32c', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['permission#create', 'role#read', 'permission#update', 'platform_feature#read', 'permission#read', 'role#update', 'role#create', 'platform_feature#create', 'platform_feature#update'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['role#create', 'billing_plan#update', 'endpoint#create', 'endpoint#update', 'feature#read', 'permission#update', 'account_settings#create', 'permission#create', 'permission#read', 'feature#update', 'account_settings#update', 'account#create', 'subscription#create', 'account_settings#read', 'account#read', 'endpoint#read', 'subscription#read', 'group#read', 'group_settings#create', 'billing_plan#create', 'group#update', 'group#create', 'feature#create', 'subscription#update', 'billing_plan#read', 'group_settings#read', 'portal_user#update', 'portal_user#read', 'role#read', 'role#update', 'portal_user#create', 'account#update', 'group_settings#update'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 20:00:27,066 [INFO] roles_api_test - -------------------------
2025-08-28 20:00:27,066 [INFO] roles_api_test - {'description': 'Platform Admin Role'}
2025-08-28 20:00:27,066 [INFO] roles_api_test - -------------------------
2025-08-28 20:00:27,066 [INFO] roles_api_test - {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}
2025-08-28 20:01:54,051 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:01:55,113 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 20:01:55,113 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '51cd9566156c8b63992536edea738160', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['role#read', 'role#create', 'platform_feature#read', 'platform_feature#create', 'platform_feature#update', 'role#update', 'permission#create', 'permission#update', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account#create', 'group_settings#update', 'group_settings#create', 'billing_plan#create', 'feature#create', 'portal_user#create', 'billing_plan#update', 'portal_user#update', 'endpoint#read', 'account_settings#create', 'permission#create', 'feature#read', 'role#create', 'billing_plan#read', 'subscription#create', 'account_settings#read', 'permission#read', 'group#update', 'account_settings#update', 'feature#update', 'role#update', 'subscription#update', 'account#read', 'group_settings#read', 'endpoint#create', 'endpoint#update', 'portal_user#read', 'subscription#read', 'account#update', 'group#create', 'role#read', 'permission#update', 'group#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 20:04:50,454 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:04:51,787 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 20:04:51,787 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': '666f3630a451bf6477f61a7c3f911bcb', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['platform_feature#create', 'platform_feature#update', 'role#read', 'role#create', 'permission#update', 'platform_feature#read', 'role#update', 'permission#create', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['account#read', 'endpoint#update', 'permission#read', 'group#update', 'group#create', 'portal_user#read', 'permission#create', 'feature#update', 'account_settings#read', 'subscription#read', 'account_settings#create', 'portal_user#create', 'subscription#create', 'account#update', 'subscription#update', 'group_settings#read', 'portal_user#update', 'group_settings#create', 'role#create', 'billing_plan#read', 'endpoint#read', 'permission#update', 'feature#create', 'account_settings#update', 'billing_plan#update', 'role#read', 'group#read', 'group_settings#update', 'billing_plan#create', 'role#update', 'account#create', 'endpoint#create', 'feature#read'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 20:04:51,789 [INFO] roles_api_test - -------------------------------
2025-08-28 20:06:29,293 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:06:30,246 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "GET /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 200 None
2025-08-28 20:06:30,246 [INFO] roles_api_test - {'success': True, 'message': 'Roles retrieved successfully', 'statusCode': 200, 'requestId': 'e4cf77f427e876488eb6d7bbcc7b5ec1', 'data': {'roles': [{'id': 'e8f5a793-6007-4612-b5d5-515715e7d50d', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'Platform Admin', 'roleKey': 'platform_admin', 'isSystem': True, 'permissions': ['platform_feature#create', 'role#read', 'role#create', 'platform_feature#update', 'role#update', 'permission#create', 'permission#update', 'platform_feature#read', 'permission#read'], 'metadata': {'description': 'Platform Admin Role'}, 'createdAt': '2025-08-28T06:57:31.62432Z', 'createdBy': '259de794-54ce-4ad2-b5c7-69a3247958fd', 'updatedAt': '2025-08-28T06:57:31.624321Z', 'updatedBy': '259de794-54ce-4ad2-b5c7-69a3247958fd'}, {'id': '18c91c16-bcc2-4781-8862-b1ffe0223d70', 'groupId': 'SANASB2C', 'groupPath': 'SANAS', 'name': 'super admin role', 'roleKey': 'super_admin', 'isSystem': True, 'permissions': ['group_settings#read', 'portal_user#update', 'account_settings#read', 'subscription#read', 'account_settings#create', 'billing_plan#read', 'role#read', 'endpoint#update', 'feature#read', 'portal_user#create', 'permission#update', 'account#update', 'role#update', 'group_settings#update', 'permission#create', 'endpoint#read', 'role#create', 'group#read', 'subscription#create', 'endpoint#create', 'group_settings#create', 'billing_plan#create', 'subscription#update', 'account_settings#update', 'account#read', 'billing_plan#update', 'portal_user#read', 'feature#update', 'permission#read', 'group#update', 'group#create', 'feature#create', 'account#create'], 'metadata': {'description': 'Default super admin role created during database initialization', 'isSystemRole': True}, 'createdAt': '2025-08-28T05:34:31.606772Z', 'createdBy': '********-0000-4000-8000-********0000', 'updatedAt': '2025-08-28T05:34:31.606772Z', 'updatedBy': ''}]}, 'meta': {'totalCount': 2}}
2025-08-28 20:06:30,246 [INFO] roles_api_test - -------------------------------
2025-08-28 20:06:30,264 [DEBUG] urllib3.connectionpool - Starting new HTTPS connection (1): console-dev-api.sanasinternal.com:443
2025-08-28 20:06:31,170 [DEBUG] urllib3.connectionpool - https://console-dev-api.sanasinternal.com:443 "POST /api/v1/groups/SANASB2C/rbac/roles HTTP/1.1" 400 111
